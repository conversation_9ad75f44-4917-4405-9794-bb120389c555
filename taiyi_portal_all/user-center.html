<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - {{companyInfo.name}}</title>
    <!-- favicons Icons -->
    
    
    
    
    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->


    <div class="page-wrapper">
        <header class="main-header clearfix">
            <div class="main-header__top">
                <div class="container">
                    <div class="main-header__top-inner">
                        <div class="main-header__top-address">
                            <ul class="list-unstyled main-header__top-address-list">
                                <li>
                                    <i class="icon">
                                        <span class="icon-pin"></span>
                                    </i>
                                    <div class="text">
                                        <p>30 Commercial Road Fratton, Australia</p>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon">
                                        <span class="icon-email"></span>
                                    </i>
                                    <div class="text">
                                        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="main-header__top-right">
                            <div class="main-header__top-menu-box">
                                <ul class="list-unstyled main-header__top-menu">
                                    <li><a href="about.html">Make a Claim</a></li>
                                    <li><a href="faq.html"> FAQs</a></li>
                                    <li><a href="about.html">About</a></li>
                                </ul>
                            </div>
                            <div class="main-header__top-social-box">
                                <div class="main-header__top-social">
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                    <a href="#"><i class="fab fa-facebook"></i></a>
                                    <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="main-menu clearfix">
                <div class="main-menu__wrapper clearfix">
                    <div class="container">
                        <div class="main-menu__wrapper-inner clearfix">
                            <div class="main-menu__left">
                                <div class="main-menu__logo">
                                    <a><img src="static/picture/logo-1.png" alt=""></a>
                                </div>
                                <div class="main-menu__main-menu-box">
                                    <div class="main-menu__main-menu-box-inner">
                                        <a href="#" class="mobile-nav__toggler"><i class="fa fa-bars"></i></a>
                                        <ul class="main-menu__list">
                                            <li class="dropdown megamenu">
                                                <a>Home </a>
                                                <ul>
                                                    <li>
                                                        <section class="home-showcase">
                                                            <div class="container">
                                                                <div class="home-showcase__inner">
                                                                    <div class="row">
                                                                        <div class="col-lg-3">
                                                                            <div class="home-showcase__item">
                                                                                <div class="home-showcase__image">
                                                                                    <img src="static/picture/home-showcase-1-1.jpg" alt="">
                                                                                    <div class="home-showcase__buttons">
                                                                                        <a class="thm-btn home-showcase__buttons__item">Multi
                                                                                            Page</a>
                                                                                        <a href="index-one-page.html" class="thm-btn home-showcase__buttons__item">One
                                                                                            Page</a>
                                                                                    </div>
                                                                                    <!-- /.home-showcase__buttons -->
                                                                                </div><!-- /.home-showcase__image -->
                                                                                <h3 class="home-showcase__title">Home 01
                                                                                </h3><!-- /.home-showcase__title -->
                                                                            </div><!-- /.home-showcase__item -->
                                                                        </div><!-- /.col-lg-3 -->
                                                                        <div class="col-lg-3">
                                                                            <div class="home-showcase__item">
                                                                                <div class="home-showcase__image">
                                                                                    <img src="static/picture/home-showcase-1-2.jpg" alt="">
                                                                                    <div class="home-showcase__buttons">
                                                                                        <a href="index2.html" class="thm-btn home-showcase__buttons__item">Multi
                                                                                            Page</a>
                                                                                        <a href="index2-one-page.html" class="thm-btn home-showcase__buttons__item">One
                                                                                            Page</a>
                                                                                    </div>
                                                                                    <!-- /.home-showcase__buttons -->
                                                                                </div><!-- /.home-showcase__image -->
                                                                                <h3 class="home-showcase__title">Home 02
                                                                                </h3><!-- /.home-showcase__title -->
                                                                            </div><!-- /.home-showcase__item -->
                                                                        </div><!-- /.col-lg-3 -->
                                                                        <div class="col-lg-3">
                                                                            <div class="home-showcase__item">
                                                                                <div class="home-showcase__image">
                                                                                    <img src="static/picture/home-showcase-1-3.jpg" alt="">
                                                                                    <div class="home-showcase__buttons">
                                                                                        <a href="index3.html" class="thm-btn home-showcase__buttons__item">Multi
                                                                                            Page</a>
                                                                                        <a href="index3-one-page.html" class="thm-btn home-showcase__buttons__item">One
                                                                                            Page</a>
                                                                                    </div>
                                                                                    <!-- /.home-showcase__buttons -->
                                                                                </div><!-- /.home-showcase__image -->
                                                                                <h3 class="home-showcase__title">Home 03
                                                                                </h3><!-- /.home-showcase__title -->
                                                                            </div><!-- /.home-showcase__item -->
                                                                        </div><!-- /.col-lg-3 -->
                                                                        <div class="col-lg-3">
                                                                            <div class="home-showcase__item">
                                                                                <div class="home-showcase__image">
                                                                                    <img src="static/picture/home-showcase-1-4.jpg" alt="">
                                                                                    <div class="home-showcase__buttons">
                                                                                        <a href="index-dark.html" class="thm-btn home-showcase__buttons__item">View
                                                                                            Page</a>
                                                                                    </div>
                                                                                    <!-- /.home-showcase__buttons -->
                                                                                </div><!-- /.home-showcase__image -->
                                                                                <h3 class="home-showcase__title">Home
                                                                                    Dark
                                                                                </h3><!-- /.home-showcase__title -->
                                                                            </div><!-- /.home-showcase__item -->
                                                                        </div><!-- /.col-lg-3 -->
                                                                    </div><!-- /.row -->
                                                                </div><!-- /.home-showcase__inner -->

                                                            </div><!-- /.container -->
                                                        </section>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li class="dropdown">
                                                <a href="#">Pages</a>
                                                <ul>
                                                    <li><a href="about.html">About</a></li>
                                                    <li><a href="portfolio.html">Portfolio</a></li>
                                                    <li><a href="portfolio-carousel.html">Portfolio Carousel</a></li>
                                                    <li><a href="portfolio-details.html">Portfolio Details</a></li>
                                                    <li><a href="team-page.html">Team</a></li>
                                                    <li><a href="team-carousel.html">Team Carousel</a></li>
                                                    <li><a href="team-details.html">Team Details</a></li>
                                                    <li><a href="testimonial.html">Testimonials</a></li>
                                                    <li><a href="testimonial-carousel.html">Teatimonials Carousel</a>
                                                    </li>
                                                    <li><a href="pricing.html">Pricing</a></li>
                                                    <li><a href="faq.html">FAQs</a></li>
                                                    <li><a href="404.html">404 Error</a></li>
                                                </ul>
                                            </li>
                                            <li class="dropdown">
                                                <a href="#">Insurance </a>
                                                <ul>
                                                    <li><a href="insurance-01.html">Insurance 01</a></li>
                                                    <li><a href="insurance-02.html">Insurance 02</a></li>
                                                    <li><a href="car-insurance.html">Car insurance</a></li>
                                                    <li><a href="life-insurance.html">Life insurance</a></li>
                                                    <li><a href="home-insurance.html">Home insurance</a>
                                                    </li>
                                                    <li><a href="health-insurance.html">Health insurance</a></li>
                                                    <li><a href="business-insurance.html">Business insurance</a></li>
                                                    <li><a href="fire-insurance.html">Fire insurance</a></li>
                                                    <li><a href="marriage-insurance.html">Marriage insurance</a></li>
                                                    <li><a href="travel-insurance.html">Travel insurance</a></li>
                                                </ul>
                                            </li>
                                            <li class="dropdown">
                                                <a href="products.html">Shop</a>
                                                <ul>
                                                    <li><a href="products.html">Shop</a></li>
                                                    <li><a href="product-details.html">Shop Details</a></li>
                                                    <li><a href="cart.html">Cart Page</a></li>
                                                    <li><a href="checkout.html">Checkout Page</a></li>
                                                </ul>
                                            </li>
                                            <li class="dropdown">
                                                <a href="#">News</a>
                                                <ul>
                                                    <li><a href="news.html">News</a></li>
                                                    <li><a href="news-carousel.html">News Carousel</a></li>
                                                    <li><a href="">News Sidebar</a></li>
                                                    <li><a href="news-details.html">News Details</a></li>
                                                </ul>
                                            </li>
                                            <li>
                                                <a href="contact.html">Contact </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="main-menu__main-menu-box-search-get-quote-btn">
                                        <div class="main-menu__main-menu-box-search">
                                            <a href="#" class="main-menu__search search-toggler icon-magnifying-glass"></a>
                                            <a href="cart.html" class="main-menu__cart insur-two-icon-shopping-cart"></a>
                                        </div>
                                        <div class="main-menu__main-menu-box-get-quote-btn-box">
                                            <a href="contact.html" class="thm-btn main-menu__main-menu-box-get-quote-btn">Get a Quote</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="main-menu__right">
                                <div class="main-menu__call">
                                    <div class="main-menu__call-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="main-menu__call-content">
                                        <a href="tel:9200368090">+92 (003) 68-090</a>
                                        <p>Call to Our Experts</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <div class="stricky-header stricked-menu main-menu">
            <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
        </div><!-- /.stricky-header -->

        <!--Page Header Start-->
        <section class="page-header">
            <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
            </div>
            <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
            <div class="container">
                <div class="page-header__inner">
                    <ul class="thm-breadcrumb list-unstyled">
                        <li><a>首页</a></li>
                        <li><span>/</span></li>
                        <li>个人中心</li>
                    </ul>
                    <h2>个人中心</h2>
                </div>
            </div>
        </section>
        <!--Page Header End-->

        <!--User Center Start-->
        <section class="news-sidebar">
            <div class="container">
                <div class="row">
                    <div class="col-xl-8 col-lg-7">
                        <div class="news-sideabr__left">
                            <!-- 个人资料概览 -->
                            <div class="user-profile-overview" style="background: #fff; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <div class="user-avatar" style="text-align: center;">
                                            <img :src="userInfo.avatar || 'static/picture/default-avatar.jpg'" alt="用户头像"
                                                 style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid #015fc9;">
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <h3 style="color: #015fc9; margin-bottom: 10px;">{{userInfo.username || '用户'}}</h3>
                                        <p style="color: #666; margin-bottom: 5px;"><i class="far fa-envelope"></i> {{userInfo.email || '未设置邮箱'}}</p>
                                        <p style="color: #666; margin-bottom: 5px;"><i class="far fa-calendar"></i> 注册时间：{{userInfo.registerTime || '2024-01-01'}}</p>
                                        <p style="color: #666; margin-bottom: 0;"><i class="far fa-eye"></i> 总浏览次数：{{browseStats.totalCount || 0}} 次</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 功能选项卡 -->
                            <div class="user-tabs" style="margin-bottom: 30px;">
                                <ul class="nav nav-tabs" style="border-bottom: 2px solid #015fc9;">
                                    <li class="nav-item">
                                        <a class="nav-link" :class="{active: activeTab === 'browse'}" @click="activeTab = 'browse'"
                                           style="color: #015fc9; border: none; padding: 15px 25px; font-weight: 500;">
                                            <i class="far fa-eye"></i> 历史浏览记录
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" :class="{active: activeTab === 'favorites'}" @click="activeTab = 'favorites'"
                                           style="color: #666; border: none; padding: 15px 25px; font-weight: 500;">
                                            <i class="far fa-heart"></i> 收藏记录
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" :class="{active: activeTab === 'likes'}" @click="activeTab = 'likes'"
                                           style="color: #666; border: none; padding: 15px 25px; font-weight: 500;">
                                            <i class="far fa-thumbs-up"></i> 点赞记录
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <!-- 历史浏览记录内容 -->
                            <div class="news-sideabr__content" v-show="activeTab === 'browse'">
                                <!-- 筛选条件 -->
                                <div class="browse-filters" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <select v-model="browseFilter.category" class="form-control" style="border: 1px solid #ddd;">
                                                <option value="">全部类别</option>
                                                <option value="company">保险公司</option>
                                                <option value="news">保险资讯</option>
                                                <option value="product">保险产品</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <input type="date" v-model="browseFilter.startDate" class="form-control" style="border: 1px solid #ddd;">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="date" v-model="browseFilter.endDate" class="form-control" style="border: 1px solid #ddd;">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" v-model="browseFilter.keyword" placeholder="搜索关键词" class="form-control" style="border: 1px solid #ddd;">
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <button @click="filterBrowseHistory" class="btn btn-primary" style="background: #015fc9; border: none; margin-right: 10px;">
                                                <i class="fas fa-search"></i> 筛选
                                            </button>
                                            <button @click="resetBrowseFilter" class="btn btn-secondary">
                                                <i class="fas fa-undo"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!--浏览记录单项-->
                                <div class="news-sideabr__single" v-for="item in filteredBrowseHistory" :key="item.id" style="margin-bottom: 25px;">
                                    <div class="news-sideabr__img">
                                        <img :src="imgUrl + (item.image || 'static/picture/default-thumb.jpg')" alt="" style="height: 200px; object-fit: cover;">
                                        <div class="browse-category-badge" :style="getCategoryBadgeStyle(item.category)">
                                            {{getCategoryName(item.category)}}
                                        </div>
                                    </div>
                                    <div class="news-sideabr__content-box">
                                        <ul class="list-unstyled news-sideabr__meta">
                                            <li><a href="#"><i class="far fa-calendar"></i> {{formatDate(item.browseTime)}}</a></li>
                                            <li><a href="#"><i class="far fa-eye"></i> 浏览时长：{{item.duration || '未知'}}</a></li>
                                        </ul>
                                        <h3 class="news-sideabr__title">
                                            <a :href="item.url" target="_blank">{{item.title}}</a>
                                        </h3>
                                        <p class="news-sideabr__text">{{item.description || '暂无描述'}}</p>
                                        <div class="news-sideabr__bottom-btn-box" style="display: flex; justify-content: space-between; align-items: center;">
                                            <a :href="item.url" target="_blank" class="news-sideabr__btn thm-btn">再次查看</a>
                                            <button @click="removeBrowseRecord(item.id)" class="btn btn-sm btn-outline-danger" style="border: 1px solid #dc3545; color: #dc3545;">
                                                <i class="fas fa-trash"></i> 删除记录
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 空状态提示 -->
                                <div v-if="filteredBrowseHistory.length === 0" class="empty-state" style="text-align: center; padding: 60px 20px; background: #f8f9fa; border-radius: 8px;">
                                    <i class="far fa-eye" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                                    <h4 style="color: #666; margin-bottom: 10px;">暂无浏览记录</h4>
                                    <p style="color: #999;">开始浏览保险公司、资讯或产品，记录会显示在这里</p>
                                </div>
                            </div>
                            <div class="news-sideabr__bottom-box">
                                <div class="news-sideabr__bottom-box-icon">
                                    <img src="static/picture/news-sideabr-bottom-box-icon.png" alt="">
                                </div>
                                <p class="news-sideabr__bottom-box-text">There are many variations of passages of Lorem
                                    Ipsum available, but majority have suffered alteration in some form, by injected
                                    humour, or randomised words which don't look even slightly believable.</p>
                            </div>
                            <div class="news-sideabr__delivering-services">
                                <div class="news-sideabr__delivering-services-icon">
                                    <a href="news-details.html"><img src="static/picture/news-sideabr__delivering-services-icon.png" alt=""></a>
                                </div>
                                <h3 class="news-sideabr__delivering-services-title"><a href="news-details.html">We’re
                                        providing the best insurance services</a></h3>
                            </div>
                            <!-- 分页加载 -->
                            <div class="news-sideabr__load-more" v-if="browseHistory.length > pageSize">
                                <button @click="loadMoreBrowseHistory" class="thm-btn news-sideabr__load-more-btn" :disabled="loading">
                                    <span v-if="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</span>
                                    <span v-else><i class="fas fa-plus"></i> 加载更多记录</span>
                                </button>
                            </div>
                            </div>

                            <!-- 收藏记录内容 -->
                            <div class="favorites-content" v-show="activeTab === 'favorites'" style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                                <i class="far fa-heart" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                                <h4 style="color: #666;">收藏功能开发中...</h4>
                                <p style="color: #999;">敬请期待</p>
                            </div>

                            <!-- 点赞记录内容 -->
                            <div class="likes-content" v-show="activeTab === 'likes'" style="padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                                <i class="far fa-thumbs-up" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                                <h4 style="color: #666;">点赞功能开发中...</h4>
                                <p style="color: #999;">敬请期待</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-lg-5">
                        <div class="sidebar">
                            <!-- 个人统计信息 -->
                            <div class="sidebar__single" style="background: linear-gradient(135deg, #015fc9, #0d7ae4); color: white; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
                                <h3 class="sidebar__title" style="color: white; margin-bottom: 20px; text-align: center;">
                                    <i class="fas fa-chart-bar"></i> 我的统计
                                </h3>
                                <div class="stats-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                    <div class="stat-item" style="text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">{{browseStats.totalCount || 0}}</div>
                                        <div style="font-size: 12px; opacity: 0.9;">总浏览</div>
                                    </div>
                                    <div class="stat-item" style="text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">{{browseStats.todayCount || 0}}</div>
                                        <div style="font-size: 12px; opacity: 0.9;">今日浏览</div>
                                    </div>
                                    <div class="stat-item" style="text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">{{browseStats.favoriteCount || 0}}</div>
                                        <div style="font-size: 12px; opacity: 0.9;">收藏数</div>
                                    </div>
                                    <div class="stat-item" style="text-align: center; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">{{browseStats.likeCount || 0}}</div>
                                        <div style="font-size: 12px; opacity: 0.9;">点赞数</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 快捷功能 -->
                            <div class="sidebar__single sidebar__category">
                                <h3 class="sidebar__title">
                                    <i class="fas fa-rocket"></i> 快捷功能
                                </h3>
                                <ul class="sidebar__category-list list-unstyled">
                                    <li :class="{active: activeTab === 'browse'}">
                                        <a href="#" @click.prevent="activeTab = 'browse'">
                                            <i class="far fa-eye"></i> 历史浏览记录
                                            <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li :class="{active: activeTab === 'favorites'}">
                                        <a href="#" @click.prevent="activeTab = 'favorites'">
                                            <i class="far fa-heart"></i> 我的收藏
                                            <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li :class="{active: activeTab === 'likes'}">
                                        <a href="#" @click.prevent="activeTab = 'likes'">
                                            <i class="far fa-thumbs-up"></i> 我的点赞
                                            <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" @click.prevent="editProfile">
                                            <i class="far fa-user"></i> 编辑资料
                                            <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" @click.prevent="logout">
                                            <i class="fas fa-sign-out-alt"></i> 退出登录
                                            <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <!-- 最近活动 -->
                            <div class="sidebar__single sidebar__comments">
                                <h3 class="sidebar__title">
                                    <i class="fas fa-clock"></i> 最近活动
                                </h3>
                                <ul class="sidebar__comments-list list-unstyled">
                                    <li v-for="activity in recentActivities" :key="activity.id">
                                        <div class="sidebar__comments-icon">
                                            <i :class="getActivityIcon(activity.type)"></i>
                                        </div>
                                        <div class="sidebar__comments-text-box">
                                            <p>{{activity.description}}</p>
                                            <small style="color: #999;">{{formatDate(activity.time)}}</small>
                                        </div>
                                    </li>
                                    <li v-if="recentActivities.length === 0">
                                        <div class="sidebar__comments-icon">
                                            <i class="fas fa-info-circle" style="color: #ccc;"></i>
                                        </div>
                                        <div class="sidebar__comments-text-box">
                                            <p style="color: #999;">暂无最近活动</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--News Sidebar End-->

        <!--Site Footer Start-->
        <footer class="site-footer">
            <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
            </div>
            <div class="container">
                <div class="site-footer__top">
                    <div class="row">
                        <div class="col-xl-3 col-lg-6 col-md-6 wow fadeInUp" data-wow-delay="100ms">
                            <div class="footer-widget__column footer-widget__about">
                                <div class="footer-widget__logo">
                                    <a><img src="static/picture/footer-logo.png" alt=""></a>
                                </div>
                                <div class="footer-widget__about-text-box">
                                    <p class="footer-widget__about-text">Aliqua id fugiat nostrud irure ex duis ea quis
                                        id quis ad et. Sunt qui esse pariatur duis deserunt.</p>
                                </div>
                                <div class="site-footer__social">
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                    <a href="#"><i class="fab fa-facebook"></i></a>
                                    <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-md-6 wow fadeInUp" data-wow-delay="200ms">
                            <div class="footer-widget__column footer-widget__contact clearfix">
                                <h3 class="footer-widget__title">Contact</h3>
                                <ul class="footer-widget__contact-list list-unstyled clearfix">
                                    <li>
                                        <div class="icon">
                                            <span class="icon-email"></span>
                                        </div>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <span class="icon-pin"></span>
                                        </div>
                                        <div class="text">
                                            <p>30 Commercial Road Fratton, Australia</p>
                                        </div>
                                    </li>
                                </ul>
                                <div class="footer-widget__open-hour">
                                    <h3 class="footer-widget__open-hour-title">Open Hours</h3>
                                    <h3 class="footer-widget__open-hour-text">Mon – Sat: 8:00 am to 6:00 pm Sunday:
                                        Closed</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-md-6 wow fadeInUp" data-wow-delay="300ms">
                            <div class="footer-widget__column footer-widget__gallery clearfix">
                                <h3 class="footer-widget__title">Instagram</h3>
                                <ul class="footer-widget__gallery-list list-unstyled clearfix">
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-1.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-2.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-3.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-4.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-5.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="footer-widget__gallery-img">
                                            <img src="static/picture/footer-widget-gallery-img-6.jpg" alt="">
                                            <a href="#"><span class="fa fa-link"></span></a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-xl-3 col-lg-6 col-md-6 wow fadeInUp" data-wow-delay="400ms">
                            <div class="footer-widget__column footer-widget__newsletter">
                                <h3 class="footer-widget__title">Newsletter</h3>
                                <p class="footer-widget__newsletter-text">Subscribe our newsletter to get our <br>
                                    latest update & news.</p>
                                <form class="footer-widget__newsletter-form">
                                    <div class="footer-widget__newsletter-input-box">
                                        <input type="email" placeholder="Email address" name="email">
                                        <button type="submit" class="footer-widget__newsletter-btn"><i class="far fa-paper-plane"></i></button>
                                    </div>
                                </form>
                                <div class="footer-widget__phone">
                                    <div class="footer-widget__phone-icon">
                                        <span class="icon-telephone"></span>
                                    </div>
                                    <div class="footer-widget__phone-text">
                                        <a href="tel:9200368090">+92 (003) 68-090</a>
                                        <p>Call to Our Experts</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="site-footer__bottom">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="site-footer__bottom-inner">
                                <p class="site-footer__bottom-text">Copyright &copy; 2022.Company name All rights reserved.<a target="_blank" href="https://sc.chinaz.com/moban/">&#x7F51;&#x9875;&#x6A21;&#x677F;</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!--Site Footer End-->


    </div><!-- /.page-wrapper -->


    <div class="mobile-nav__wrapper">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
        <!-- /.mobile-nav__overlay -->
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143" alt=""></a>
            </div>
            <!-- /.logo-box -->
            <div class="mobile-nav__container"></div>
            <!-- /.mobile-nav__container -->

            <ul class="mobile-nav__contact list-unstyled">
                <li>
                    <i class="fa fa-envelope"></i>
                    <a href="mailto:needhelp@packageName__.com"><EMAIL></a>
                </li>
                <li>
                    <i class="fa fa-phone-alt"></i>
                    <a href="tel:************">************</a>
                </li>
            </ul><!-- /.mobile-nav__contact -->
            <div class="mobile-nav__top">
                <div class="mobile-nav__social">
                    <a href="#" class="fab fa-twitter"></a>
                    <a href="#" class="fab fa-facebook-square"></a>
                    <a href="#" class="fab fa-pinterest-p"></a>
                    <a href="#" class="fab fa-instagram"></a>
                </div><!-- /.mobile-nav__social -->
            </div><!-- /.mobile-nav__top -->



        </div>
        <!-- /.mobile-nav__content -->
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script>
      //const jeeApi = "https://www.taiyibx.com";
      const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                activeTab: 'browse', // 当前激活的选项卡
                loading: false,
                pageSize: 10,
                currentPage: 1,

                // 用户信息
                userInfo: {
                    username: '张三',
                    email: '<EMAIL>',
                    avatar: '',
                    registerTime: '2024-01-15'
                },

                // 浏览统计
                browseStats: {
                    totalCount: 156,
                    todayCount: 8,
                    favoriteCount: 23,
                    likeCount: 45
                },

                // 浏览记录
                browseHistory: [
                    {
                        id: 1,
                        title: '中国人寿保险股份有限公司',
                        description: '中国人寿保险股份有限公司是中国最大的人寿保险公司，为客户提供全面的保险和投资服务。',
                        category: 'company',
                        image: 'static/picture/company-1.jpg',
                        url: 'company.html?id=1',
                        browseTime: '2024-01-20 14:30:00',
                        duration: '5分钟'
                    },
                    {
                        id: 2,
                        title: '2024年保险行业发展趋势分析',
                        description: '深入分析2024年保险行业的发展趋势，包括数字化转型、产品创新等方面。',
                        category: 'news',
                        image: 'static/picture/news-1.jpg',
                        url: 'news-details.html?id=2',
                        browseTime: '2024-01-20 10:15:00',
                        duration: '8分钟'
                    },
                    {
                        id: 3,
                        title: '重疾险产品对比分析',
                        description: '全面对比市场上主流重疾险产品的保障范围、价格和理赔条件。',
                        category: 'product',
                        image: 'static/picture/product-1.jpg',
                        url: 'product-details.html?id=3',
                        browseTime: '2024-01-19 16:45:00',
                        duration: '12分钟'
                    }
                ],

                // 筛选条件
                browseFilter: {
                    category: '',
                    startDate: '',
                    endDate: '',
                    keyword: ''
                },

                // 最近活动
                recentActivities: [
                    {
                        id: 1,
                        type: 'browse',
                        description: '浏览了中国人寿保险公司',
                        time: '2024-01-20 14:30:00'
                    },
                    {
                        id: 2,
                        type: 'favorite',
                        description: '收藏了重疾险产品对比',
                        time: '2024-01-20 10:15:00'
                    }
                ],

                companyInfo: {},
                imgUrl: jeeApi + '/jeecg-boot/'
            },
            computed: {
                filteredBrowseHistory() {
                    let filtered = this.browseHistory;

                    // 按类别筛选
                    if (this.browseFilter.category) {
                        filtered = filtered.filter(item => item.category === this.browseFilter.category);
                    }

                    // 按关键词筛选
                    if (this.browseFilter.keyword) {
                        const keyword = this.browseFilter.keyword.toLowerCase();
                        filtered = filtered.filter(item =>
                            item.title.toLowerCase().includes(keyword) ||
                            item.description.toLowerCase().includes(keyword)
                        );
                    }

                    // 按日期筛选
                    if (this.browseFilter.startDate) {
                        filtered = filtered.filter(item =>
                            new Date(item.browseTime) >= new Date(this.browseFilter.startDate)
                        );
                    }

                    if (this.browseFilter.endDate) {
                        filtered = filtered.filter(item =>
                            new Date(item.browseTime) <= new Date(this.browseFilter.endDate + ' 23:59:59')
                        );
                    }

                    return filtered;
                }
            },
            mounted() {
                this.getCompanyInfo();
                this.loadUserInfo();
            },
            methods: {
                // 获取公司信息
                async getCompanyInfo() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                            method: 'get',
                        });
                        const data = await response.json();
                        this.companyInfo = data.result;
                    } catch (error) {
                        console.error('获取公司信息失败:', error);
                    }
                },

                // 加载用户信息
                loadUserInfo() {
                    // 这里可以从 localStorage 或 API 获取用户信息
                    const savedUserInfo = localStorage.getItem('userInfo');
                    if (savedUserInfo) {
                        this.userInfo = JSON.parse(savedUserInfo);
                    }
                },

                // 格式化日期
                formatDate(dateString) {
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                },

                // 获取类别名称
                getCategoryName(category) {
                    const categoryMap = {
                        'company': '保险公司',
                        'news': '保险资讯',
                        'product': '保险产品'
                    };
                    return categoryMap[category] || '其他';
                },

                // 获取类别徽章样式
                getCategoryBadgeStyle(category) {
                    const styleMap = {
                        'company': 'position: absolute; top: 10px; right: 10px; background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;',
                        'news': 'position: absolute; top: 10px; right: 10px; background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;',
                        'product': 'position: absolute; top: 10px; right: 10px; background: #ffc107; color: black; padding: 4px 8px; border-radius: 4px; font-size: 12px;'
                    };
                    return styleMap[category] || 'position: absolute; top: 10px; right: 10px; background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;';
                },

                // 获取活动图标
                getActivityIcon(type) {
                    const iconMap = {
                        'browse': 'far fa-eye',
                        'favorite': 'far fa-heart',
                        'like': 'far fa-thumbs-up'
                    };
                    return iconMap[type] || 'fas fa-info-circle';
                },

                // 筛选浏览记录
                filterBrowseHistory() {
                    // 筛选逻辑在 computed 中处理
                    console.log('筛选浏览记录');
                },

                // 重置筛选条件
                resetBrowseFilter() {
                    this.browseFilter = {
                        category: '',
                        startDate: '',
                        endDate: '',
                        keyword: ''
                    };
                },

                // 删除浏览记录
                removeBrowseRecord(id) {
                    if (confirm('确定要删除这条浏览记录吗？')) {
                        this.browseHistory = this.browseHistory.filter(item => item.id !== id);
                        // 这里可以调用 API 删除服务器端的记录
                    }
                },

                // 加载更多浏览记录
                loadMoreBrowseHistory() {
                    this.loading = true;
                    // 模拟 API 调用
                    setTimeout(() => {
                        this.loading = false;
                        // 这里可以加载更多数据
                    }, 1000);
                },

                // 编辑个人资料
                editProfile() {
                    alert('编辑个人资料功能开发中...');
                },

                // 退出登录
                logout() {
                    if (confirm('确定要退出登录吗？')) {
                        localStorage.removeItem('userInfo');
                        window.location.href = 'index.html';
                    }
                }
            }
        });
    </script>

    <style>
        /* 选项卡样式 */
        .nav-tabs .nav-link.active {
            background-color: #015fc9 !important;
            color: white !important;
            border-color: #015fc9 !important;
        }

        .nav-tabs .nav-link:hover {
            background-color: #f8f9fa;
            border-color: #015fc9;
        }

        /* 侧边栏激活状态 */
        .sidebar__category-list li.active a {
            background-color: #015fc9;
            color: white !important;
        }

        /* 筛选表单样式 */
        .form-control:focus {
            border-color: #015fc9;
            box-shadow: 0 0 0 0.2rem rgba(1, 95, 201, 0.25);
        }

        /* 按钮悬停效果 */
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 卡片悬停效果 */
        .news-sideabr__single:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
    </style>
</body>

</html>